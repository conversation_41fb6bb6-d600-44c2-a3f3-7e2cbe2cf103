"""
# @Time    : 2023/12/28 22:28
# <AUTHOR> <PERSON><PERSON><PERSON>.<PERSON>
# @File    : pg_client.py
# @Desc    : 
"""
import psycopg2
import psycopg2.extras


class Database:
    def __init__(self):
        self.dbname = 'fine_data'
        self.user = 'chaunceyli'
        self.password = 'a8A@RceKLldMRMF5'
        self.host = '*************'
        self.port = '27432'

    def connect(self):
        try:
            conn = psycopg2.connect(
                dbname=self.dbname,
                user=self.user,
                password=self.password,
                host=self.host,
                port=self.port
            )
            return conn
        except psycopg2.Error as e:
            print(f"An error occurred while connecting to the database: {e}")
            return None

    def execute_sql(self, sql, params=None):
        """
        执行 SQL 语句并返回结果
        
        Args:
            sql (str): 要执行的 SQL 语句
            params (tuple, optional): SQL 参数，用于防止 SQL 注入
            
        Returns:
            list: 对于 SELECT 查询，返回查询结果列表
            int: 对于 INSERT/UPDATE/DELETE，返回受影响的行数
            None: 如果执行失败
        """
        conn = None
        cursor = None
        try:
            # 建立连接
            conn = self.connect()
            if conn is None:
                return None
            
            # 创建游标
            cursor = conn.cursor()
            
            # 执行 SQL
            if params:
                cursor.execute(sql, params)
            else:
                cursor.execute(sql)
            
            # 判断 SQL 类型并处理结果
            sql_upper = sql.strip().upper()
            if sql_upper.startswith('SELECT'):
                # 对于查询语句，获取所有结果
                results = cursor.fetchall()
                return results
            else:
                # 对于增删改语句，提交事务并返回受影响行数
                conn.commit()
                return cursor.rowcount
                
        except psycopg2.Error as e:
            print(f"SQL 执行出错: {e}")
            if conn:
                conn.rollback()
            return None
        except Exception as e:
            print(f"执行过程中发生未知错误: {e}")
            if conn:
                conn.rollback()
            return None
        finally:
            # 确保资源正确释放
            if cursor:
                cursor.close()
            if conn:
                conn.close()

    def execute_query(self, sql, params=None):
        """
        专门用于执行查询语句的方法，返回字典格式的结果
        
        Args:
            sql (str): SELECT 查询语句
            params (tuple, optional): SQL 参数
            
        Returns:
            list: 包含字典的列表，每个字典代表一行数据
            None: 如果执行失败
        """
        conn = None
        cursor = None
        try:
            # 建立连接
            conn = self.connect()
            if conn is None:
                return None
            
            # 创建游标，使用 RealDictCursor 返回字典格式结果
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            
            # 执行查询
            if params:
                cursor.execute(sql, params)
            else:
                cursor.execute(sql)
            
            # 获取结果
            results = cursor.fetchall()
            # 转换为普通字典列表
            return [dict(row) for row in results]
                
        except psycopg2.Error as e:
            print(f"查询执行出错: {e}")
            return None
        except Exception as e:
            print(f"查询过程中发生未知错误: {e}")
            return None
        finally:
            # 确保资源正确释放
            if cursor:
                cursor.close()
            if conn:
                conn.close()