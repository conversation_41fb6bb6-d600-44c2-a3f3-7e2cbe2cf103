# -*- coding: utf-8 -*-
"""
@Time ： 2024/11/22 下午4:00
@Auth ： 李昌鑫
@File ：push_datahub_cpt_infos.py
@IDE ：PyCharm
@Motto：
"""
from datahub_dev.config import api_token

import datahub.emitter.mce_builder as builder
from datahub.emitter.rest_emitter import DatahubRestEmitter
import requests
import json

from utils_new.pg_client import Database
db = Database()



def push_dataset_metadata(dataset_name, fields, display_name=None, description=None):
    """
    向DataHub推送数据集元数据

    参数:
        dataset_name (str): 数据集名称（用作URN）
        fields (list): 字段定义列表,包含名称、描述和类型
        display_name (str, optional): 显示名称
        description (str, optional): 数据集描述
    """
    url = "http://*************:8080/entities?action=ingest"

    # 构建基础的aspects
    aspects = [{
        "com.linkedin.schema.SchemaMetadata": {
            "schemaName": "ITFDL",
            "platform": "urn:li:dataPlatform:FineDataLink",
            "version": 0,
            "hash": "",
            "platformSchema": {"com.linkedin.schema.MySqlDDL": {"tableSchema": "CRM"}},
            "fields": fields
        }
    }]
    
    # 如果有显示名称或描述，添加数据集属性
    if display_name or description:
        dataset_properties = {}
        if display_name:
            dataset_properties["name"] = display_name
        if description:
            dataset_properties["description"] = description
            
        aspects.append({
            "com.linkedin.dataset.DatasetProperties": dataset_properties
        })

    payload = {
        "entity": {
            "value": {
                "com.linkedin.metadata.snapshot.DatasetSnapshot": {
                    "urn": f"urn:li:dataset:(urn:li:dataPlatform:FineDataLink,{dataset_name},PROD)",
                    "aspects": aspects
                }
            }
        }
    }

    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {api_token}'
    }

    try:
        response = requests.post(url, headers=headers, json=payload)
        
        print(f"   HTTP状态码: {response.status_code}")
        print(f"   响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            return response.text if response.text else "SUCCESS"
        else:
            return f"HTTP_ERROR_{response.status_code}: {response.text}"
            
    except Exception as e:
        return f"EXCEPTION_ERROR: {str(e)}"


def push_fdl_task_info(task_data):
    """
    推送FDL任务信息到DataHub
    
    Args:
        task_data (tuple): 包含FDL任务信息的元组
        格式: (task_id, task_name, task_create_time, task_check_status, 
              task_online_time, task_online_status, task_desc, create_userid, 
              create_user, task_path)
    
    Returns:
        str: API响应结果
    """
    # 解析任务数据
    task_id, task_name, task_create_time, task_check_status, task_online_time, \
    task_online_status, task_desc, create_userid, create_user, task_path = task_data
    
    # 格式化时间
    create_time_str = task_create_time.strftime("%Y-%m-%d %H:%M:%S") if task_create_time else ""
    online_time_str = task_online_time.strftime("%Y-%m-%d %H:%M:%S") if task_online_time else ""
    
    # 构建字段信息
    fields = [
        {
            "fieldPath": "任务ID",
            "description": str(task_id) if task_id else "",
            "nativeDataType": "varchar(50)",
            "type": {"type": {"com.linkedin.schema.StringType": {}}}
        },
        {
            "fieldPath": "任务名称",
            "description": str(task_name) if task_name else "",
            "nativeDataType": "varchar(100)",
            "type": {"type": {"com.linkedin.schema.StringType": {}}}
        },
        {
            "fieldPath": "创建时间",
            "description": create_time_str if create_time_str else "",
            "nativeDataType": "timestamp",
            "type": {"type": {"com.linkedin.schema.TimeType": {}}}
        },
        {
            "fieldPath": "校验状态",
            "description": str(task_check_status) if task_check_status else "",
            "nativeDataType": "varchar(20)",
            "type": {"type": {"com.linkedin.schema.StringType": {}}}
        },
        {
            "fieldPath": "上线时间",
            "description": online_time_str if online_time_str else "",
            "nativeDataType": "timestamp",
            "type": {"type": {"com.linkedin.schema.TimeType": {}}}
        },
        {
            "fieldPath": "上线状态",
            "description": str(task_online_status) if task_online_status else "",
            "nativeDataType": "varchar(20)",
            "type": {"type": {"com.linkedin.schema.StringType": {}}}
        },
        {
            "fieldPath": "任务描述",
            "description": str(task_desc) if task_desc else "",
            "nativeDataType": "text",
            "type": {"type": {"com.linkedin.schema.StringType": {}}}
        },
        {
            "fieldPath": "创建用户ID",
            "description": str(create_userid) if create_userid else "",
            "nativeDataType": "varchar(50)",
            "type": {"type": {"com.linkedin.schema.StringType": {}}}
        },
        {
            "fieldPath": "创建用户",
            "description": str(create_user) if create_user else "",
            "nativeDataType": "varchar(50)",
            "type": {"type": {"com.linkedin.schema.StringType": {}}}
        },
        {
            "fieldPath": "文件路径",
            "description": str(task_path) if task_path else "",
            "nativeDataType": "text",
            "type": {"type": {"com.linkedin.schema.StringType": {}}}
        }
    ]
    
    # 使用任务ID作为URN的唯一标识符
    dataset_urn_name = f"{task_id}"
    
    # 使用任务名称作为显示名称
    display_name = str(task_name) if task_name else f"FDL任务_{task_id}"
    
    # 构建描述信息
    description_parts = []
    if create_user:
        description_parts.append(f"创建者: {create_user}")
    if task_check_status:
        description_parts.append(f"状态: {task_check_status}")
    if create_time_str:
        description_parts.append(f"创建时间: {create_time_str}")
    
    description = " | ".join(description_parts) if description_parts else (str(task_desc) if task_desc else "")
    
    return push_dataset_metadata(dataset_urn_name, fields, display_name)

if __name__ == "__main__":
    results = db.execute_sql("""
                             SELECT *
                             FROM public.test_fdl_it_task_data_info limit 1
                             """)
    
    # 解析返回的数据
    def parse_fdl_task_data(results):
        """
        解析FDL任务数据的函数
        """
        if not results:
            print("没有查询到数据")
            return
        
        # 定义字段名
        columns = [
            'task_id',           # 任务ID
            'task_name',         # 定时任务名称
            'task_create_time',  # 任务创建时间
            'task_check_status', # 任务校验状态
            'task_online_time',  # 任务上线时间
            'task_online_status',# 任务上线状态
            'task_desc',         # 任务描述
            'create_userid',     # 任务创建用户ID
            'create_user',       # 任务创建用户名
            'task_path'          # 文件目录路径
        ]
        
        print(f"\n=== 查询到 {len(results)} 条FDL任务数据 ===\n")
        
        for i, row in enumerate(results, 1):
            print(f"【第 {i} 条记录】")
            print("-" * 50)
            
            for j, value in enumerate(row):
                column_name = columns[j]
                # 格式化显示
                if value is None:
                    display_value = "无"
                elif isinstance(value, str) and len(value) > 50:
                    display_value = value[:50] + "..."
                else:
                    display_value = value
                    
                print(f"{column_name:18}: {display_value}")
            print()
    
    # 调用解析函数
    parse_fdl_task_data(results)
    
    # 推送FDL任务信息到DataHub
    print("\n=== 开始推送FDL任务信息到DataHub ===\n")
    
    success_count = 0
    fail_count = 0
    
    for i, task_data in enumerate(results, 1):
        try:
            print(f"正在推送第 {i} 条任务: {task_data[1]}...")
            
            # 推送任务信息
            response = push_fdl_task_info(task_data)
            
            # 检查响应
            print(f"   响应类型: {type(response)}")
            print(f"   响应长度: {len(str(response)) if response else 0}")
            print(f"   响应内容: '{response}'")
            
            if response and len(str(response).strip()) > 0 and "error" not in response.lower():
                print(f"✅ 任务 '{task_data[1]}' 推送成功")
                success_count += 1
            else:
                print(f"❌ 任务 '{task_data[1]}' 推送失败")
                if not response:
                    print(f"   失败原因: 响应为空")
                elif len(str(response).strip()) == 0:
                    print(f"   失败原因: 响应为空字符串")
                elif "error" in response.lower():
                    print(f"   失败原因: 响应包含错误信息")
                    print(f"   错误详情: {response}")
                else:
                    print(f"   失败原因: 未知")
                    print(f"   响应内容: {response}")
                fail_count += 1
                
        except Exception as e:
            print(f"❌ 任务 '{task_data[1]}' 推送异常: {str(e)}")
            fail_count += 1
        
        print("-" * 50)
    
    # 输出统计结果
    print(f"\n=== 推送完成统计 ===")
    print(f"总共处理: {len(results)} 条任务")
    print(f"推送成功: {success_count} 条")
    print(f"推送失败: {fail_count} 条")
    print(f"成功率: {(success_count/len(results)*100):.1f}%")