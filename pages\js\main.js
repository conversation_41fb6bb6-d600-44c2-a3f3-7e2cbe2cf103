// 主应用类
class DataMapApp {
    constructor() {
        this.init();
    }

    async init() {
        // 显示加载动画
        this.showLoading();
        
        // 初始化各个组件
        await this.initComponents();
        
        // 隐藏加载动画
        this.hideLoading();
        
        // 启动动画效果
        this.startAnimations();
    }

    async initComponents() {
        // 初始化粒子背景
        this.initParticles();

        // 初始化统计数据
        this.initStatistics();

        // 初始化数据平台导航
        this.initPlatforms();

        // 初始化资产分类导航
        this.initAssetCategories();

        // 初始化业务域
        this.initDomains();

        // 初始化数据血缘关系图
        this.initDataLineage();

        // 初始化热门数据资产
        this.initPopularAssets();

        // 初始化数据质量
        this.initDataQuality();

        // 初始化搜索功能
        this.initSearch();

        // 初始化模态框
        this.initModal();

        // 模拟加载延迟
        await new Promise(resolve => setTimeout(resolve, 1500));
    }

    // 粒子背景动画
    initParticles() {
        const container = document.getElementById('particles-container');
        const particleCount = 50;

        for (let i = 0; i < particleCount; i++) {
            setTimeout(() => {
                this.createParticle(container);
            }, i * 100);
        }

        // 持续创建粒子
        setInterval(() => {
            this.createParticle(container);
        }, 2000);
    }

    createParticle(container) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        
        // 随机位置和大小
        particle.style.left = Math.random() * 100 + '%';
        particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
        particle.style.animationDelay = Math.random() * 2 + 's';
        
        // 随机颜色 - 更丰富的色彩
        const colors = ['#4285f4', '#fd79a8', '#fdcb6e', '#26de81', '#ff6b6b', '#a29bfe', '#55a3ff'];
        particle.style.background = colors[Math.floor(Math.random() * colors.length)];
        
        container.appendChild(particle);
        
        // 动画结束后移除粒子
        setTimeout(() => {
            if (particle.parentNode) {
                particle.parentNode.removeChild(particle);
            }
        }, 8000);
    }

    // 初始化统计数据
    initStatistics() {
        const totalAssets = document.getElementById('total-assets');
        const totalDomains = document.getElementById('total-domains');
        const totalApis = document.getElementById('total-apis');
        const totalReports = document.getElementById('total-reports');

        this.animateNumber(totalAssets, DATA_CONFIG.statistics.totalAssets);
        this.animateNumber(totalDomains, DATA_CONFIG.statistics.totalDomains);
        this.animateNumber(totalApis, DATA_CONFIG.statistics.totalApis);
        this.animateNumber(totalReports, DATA_CONFIG.statistics.totalReports);
    }

    // 数字动画
    animateNumber(element, target) {
        let current = 0;
        const increment = target / 50;
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            element.textContent = Math.floor(current);
        }, 40);
    }

    // 初始化数据平台导航
    initPlatforms() {
        // 为平台导航创建一个新的section，插入到资产分类之前
        const mainContent = document.querySelector('.main-content');
        const assetCategoriesSection = document.querySelector('.asset-categories');

        const platformsSection = document.createElement('section');
        platformsSection.className = 'platforms-overview';
        platformsSection.innerHTML = `
            <h2 class="section-title">
                <i class="fas fa-server"></i>
                数据平台概览
            </h2>
            <div class="platforms-grid" id="platforms-grid">
                <!-- 动态生成平台卡片 -->
            </div>
        `;

        mainContent.insertBefore(platformsSection, assetCategoriesSection);

        const container = document.getElementById('platforms-grid');

        DATA_CONFIG.platforms.forEach((platform, index) => {
            const item = this.createPlatformItem(platform);
            container.appendChild(item);

            // 延迟显示动画
            setTimeout(() => {
                item.style.opacity = '0';
                item.style.transform = 'translateY(-20px)';
                item.style.transition = 'all 0.4s ease';

                setTimeout(() => {
                    item.style.opacity = '1';
                    item.style.transform = 'translateY(0)';
                }, 50);
            }, index * 80);
        });
    }

    createPlatformItem(platform) {
        const item = document.createElement('div');
        item.className = 'platform-item';
        item.style.setProperty('--platform-color', platform.color);
        item.innerHTML = `
            <div class="platform-icon">
                <i class="${platform.icon}"></i>
            </div>
            <div class="platform-name">${platform.name}</div>
            <div class="platform-count">${platform.count.toLocaleString()} 个资产</div>
            <div class="platform-type">${this.getPlatformTypeText(platform.type)}</div>
        `;

        item.addEventListener('click', () => {
            // 移除其他活跃状态
            document.querySelectorAll('.platform-item').forEach(el => el.classList.remove('active'));
            // 添加活跃状态
            item.classList.add('active');
            // 筛选显示对应平台的资产
            this.filterAssetsByPlatform(platform.id);
        });

        return item;
    }

    getPlatformTypeText(type) {
        const typeMap = {
            'database': '数据库',
            'report': '报表平台',
            'bi': 'BI平台',
            'integration': '数据集成',
            'application': '应用平台'
        };
        return typeMap[type] || type;
    }

    filterAssetsByPlatform(platformId) {
        // 这里可以实现按平台筛选资产的逻辑
        console.log('筛选平台资产:', platformId);
    }

    // 初始化资产分类导航
    initAssetCategories() {
        const container = document.getElementById('categories-nav');

        DATA_CONFIG.assetCategories.forEach((category, index) => {
            const item = this.createCategoryItem(category);
            container.appendChild(item);

            // 延迟显示动画
            setTimeout(() => {
                item.style.opacity = '0';
                item.style.transform = 'translateY(-20px)';
                item.style.transition = 'all 0.4s ease';

                setTimeout(() => {
                    item.style.opacity = '1';
                    item.style.transform = 'translateY(0)';
                }, 50);
            }, index * 80);
        });
    }

    createCategoryItem(category) {
        const item = document.createElement('div');
        item.className = 'category-item';
        item.style.setProperty('--category-color', category.color);
        item.innerHTML = `
            <div class="category-icon">
                <i class="${category.icon}"></i>
            </div>
            <div class="category-name">${category.name}</div>
            <div class="category-count">${category.count} 个资产</div>
        `;

        item.addEventListener('click', () => {
            // 移除其他活跃状态
            document.querySelectorAll('.category-item').forEach(el => el.classList.remove('active'));
            // 添加活跃状态
            item.classList.add('active');
            // 筛选显示对应类型的资产
            this.filterAssetsByCategory(category.id);
        });

        return item;
    }

    filterAssetsByCategory(categoryId) {
        // 这里可以实现按分类筛选资产的逻辑
        console.log('筛选资产分类:', categoryId);
    }

    // 初始化业务域
    initDomains() {
        const container = document.getElementById('domains-grid');
        
        DATA_CONFIG.domains.forEach((domain, index) => {
            const card = this.createDomainCard(domain);
            container.appendChild(card);
            
            // 延迟显示动画
            setTimeout(() => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'all 0.5s ease';
                
                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, 100);
            }, index * 100);
        });
    }

    createDomainCard(domain) {
        const card = document.createElement('div');
        card.className = 'domain-card';
        card.style.setProperty('--domain-color', domain.color);
        card.innerHTML = `
            <div class="domain-header">
                <h3 class="domain-name">${domain.name}</h3>
                <i class="${domain.icon} domain-icon"></i>
            </div>
            <div class="domain-stats">
                <div class="domain-stat">
                    <span class="domain-stat-number">${domain.assetCount}</span>
                    <span class="domain-stat-label">数据资产</span>
                </div>
                <div class="domain-stat">
                    <span class="domain-stat-number">${domain.apiCount}</span>
                    <span class="domain-stat-label">API接口</span>
                </div>
                <div class="domain-stat">
                    <span class="domain-stat-number">${domain.reportCount}</span>
                    <span class="domain-stat-label">报表</span>
                </div>
            </div>
            <p class="domain-description">${domain.description}</p>
            <div class="domain-asset-types">
                <div class="asset-type-tags">
                    ${domain.assetTypes.map(type => `<span class="asset-type-tag">${type}</span>`).join('')}
                </div>
            </div>
        `;

        card.addEventListener('click', () => {
            this.showDomainDetail(domain);
        });

        return card;
    }

    // 初始化数据血缘关系图
    initDataLineage() {
        const svg = document.getElementById('lineage-svg');
        const { nodes, links } = DATA_CONFIG.dataLineage;

        // 清空SVG
        svg.innerHTML = '';

        // 创建渐变和图案定义
        const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');

        // 创建不同类型节点的渐变 - 丰富色彩
        const gradients = [
            { id: 'mysqlGradient', colors: ['#4285f4', '#1a73e8'] }, // Google蓝
            { id: 'postgresqlGradient', colors: ['#336791', '#2d5a87'] }, // PostgreSQL蓝
            { id: 'jiandaoyunGradient', colors: ['#00d4aa', '#00b894'] }, // 青绿色
            { id: 'finedatalinkGradient', colors: ['#ff6b6b', '#e55353'] }, // 珊瑚红
            { id: 'warehouseGradient', colors: ['#a29bfe', '#6c5ce7'] }, // 紫色
            { id: 'finereportGradient', colors: ['#fd79a8', '#e84393'] }, // 粉红色
            { id: 'finebiGradient', colors: ['#fdcb6e', '#e17055'] }, // 橙色
            { id: 'apiGradient', colors: ['#55a3ff', '#3742fa'] }, // 亮蓝色
            { id: 'usersGradient', colors: ['#26de81', '#20bf6b'] } // 绿色
        ];

        gradients.forEach(grad => {
            const gradient = document.createElementNS('http://www.w3.org/2000/svg', 'linearGradient');
            gradient.setAttribute('id', grad.id);
            gradient.innerHTML = `
                <stop offset="0%" style="stop-color:${grad.colors[0]};stop-opacity:0.8" />
                <stop offset="100%" style="stop-color:${grad.colors[1]};stop-opacity:0.6" />
            `;
            defs.appendChild(gradient);
        });

        svg.appendChild(defs);

        // 创建连接线和标签
        links.forEach((link, index) => {
            const sourceNode = nodes.find(n => n.id === link.source);
            const targetNode = nodes.find(n => n.id === link.target);

            // 创建连接线
            const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
            line.setAttribute('x1', sourceNode.x);
            line.setAttribute('y1', sourceNode.y);
            line.setAttribute('x2', targetNode.x);
            line.setAttribute('y2', targetNode.y);
            line.setAttribute('class', 'flow-link');
            line.setAttribute('stroke', link.color || '#6c757d');
            line.setAttribute('stroke-width', '2');
            line.setAttribute('marker-end', 'url(#arrowhead)');

            // 添加动画延迟
            line.style.animationDelay = `${index * 0.3}s`;

            svg.appendChild(line);

            // 添加连接线标签
            const midX = (sourceNode.x + targetNode.x) / 2;
            const midY = (sourceNode.y + targetNode.y) / 2;

            const labelBg = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
            labelBg.setAttribute('x', midX - 25);
            labelBg.setAttribute('y', midY - 8);
            labelBg.setAttribute('width', 50);
            labelBg.setAttribute('height', 16);
            labelBg.setAttribute('fill', '#ffffff');
            labelBg.setAttribute('stroke', link.color || '#6c757d');
            labelBg.setAttribute('stroke-width', '1');
            labelBg.setAttribute('rx', '3');
            labelBg.setAttribute('opacity', '0.9');

            const label = document.createElementNS('http://www.w3.org/2000/svg', 'text');
            label.setAttribute('x', midX);
            label.setAttribute('y', midY + 3);
            label.setAttribute('class', 'flow-label');
            label.setAttribute('font-size', '8px');
            label.setAttribute('fill', link.color || '#6c757d');
            label.textContent = link.label;

            svg.appendChild(labelBg);
            svg.appendChild(label);

            // 添加流动粒子
            setTimeout(() => {
                this.createFlowParticle(svg, sourceNode, targetNode, link.color);
            }, index * 300);
        });

        // 添加箭头标记定义
        const arrowMarker = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
        arrowMarker.setAttribute('id', 'arrowhead');
        arrowMarker.setAttribute('markerWidth', '10');
        arrowMarker.setAttribute('markerHeight', '7');
        arrowMarker.setAttribute('refX', '9');
        arrowMarker.setAttribute('refY', '3.5');
        arrowMarker.setAttribute('orient', 'auto');

        const arrowPath = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
        arrowPath.setAttribute('points', '0 0, 10 3.5, 0 7');
        arrowPath.setAttribute('fill', '#6c757d');

        arrowMarker.appendChild(arrowPath);
        defs.appendChild(arrowMarker);

        // 创建节点
        nodes.forEach((node, index) => {
            const group = document.createElementNS('http://www.w3.org/2000/svg', 'g');
            group.setAttribute('class', 'flow-node-group');

            // 创建节点背景
            const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
            rect.setAttribute('x', node.x - 40);
            rect.setAttribute('y', node.y - 25);
            rect.setAttribute('width', 80);
            rect.setAttribute('height', 50);
            rect.setAttribute('rx', 8);
            rect.setAttribute('class', 'flow-node');
            rect.setAttribute('fill', this.getNodeGradient(node.type));
            rect.setAttribute('stroke', '#495057');
            rect.setAttribute('stroke-width', '2');

            // 创建图标
            const iconBg = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
            iconBg.setAttribute('cx', node.x);
            iconBg.setAttribute('cy', node.y - 10);
            iconBg.setAttribute('r', 8);
            iconBg.setAttribute('fill', '#ffffff');
            iconBg.setAttribute('stroke', '#495057');
            iconBg.setAttribute('stroke-width', '1');

            // 创建文本（分两行显示）
            const lines = node.name.split('\n');
            lines.forEach((line, lineIndex) => {
                const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
                text.setAttribute('x', node.x);
                text.setAttribute('y', node.y + 8 + (lineIndex * 10));
                text.setAttribute('class', 'flow-text');
                text.setAttribute('font-size', '10px');
                text.setAttribute('font-weight', lineIndex === 0 ? 'bold' : 'normal');
                text.textContent = line;
                group.appendChild(text);
            });

            // 添加悬停效果
            rect.addEventListener('mouseenter', () => {
                rect.setAttribute('stroke-width', '3');
                rect.setAttribute('fill-opacity', '0.9');
            });

            rect.addEventListener('mouseleave', () => {
                rect.setAttribute('stroke-width', '2');
                rect.setAttribute('fill-opacity', '0.8');
            });

            group.appendChild(rect);
            group.appendChild(iconBg);
            svg.appendChild(group);

            // 延迟显示动画
            setTimeout(() => {
                group.style.opacity = '0';
                group.style.transition = 'opacity 0.5s ease';
                setTimeout(() => {
                    group.style.opacity = '1';
                }, 100);
            }, index * 150);
        });
    }

    getNodeGradient(nodeType) {
        const gradientMap = {
            'mysql': 'url(#mysqlGradient)',
            'postgresql': 'url(#postgresqlGradient)',
            'jiandaoyun': 'url(#jiandaoyunGradient)',
            'finedatalink': 'url(#finedatalinkGradient)',
            'warehouse': 'url(#warehouseGradient)',
            'datamart': 'url(#warehouseGradient)',
            'finereport': 'url(#finereportGradient)',
            'finebi': 'url(#finebiGradient)',
            'api': 'url(#apiGradient)',
            'users': 'url(#usersGradient)',
            'external': 'url(#apiGradient)'
        };
        return gradientMap[nodeType] || 'url(#mysqlGradient)';
    }

    // 创建流动粒子
    createFlowParticle(svg, sourceNode, targetNode, color = '#6c757d') {
        const particle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        particle.setAttribute('cx', sourceNode.x);
        particle.setAttribute('cy', sourceNode.y);
        particle.setAttribute('r', 2);
        particle.setAttribute('class', 'flow-particle');
        particle.setAttribute('fill', color);
        particle.setAttribute('opacity', '0');

        svg.appendChild(particle);

        // 计算移动路径
        const dx = targetNode.x - sourceNode.x;
        const dy = targetNode.y - sourceNode.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        const duration = distance * 15; // 根据距离调整动画时间

        // 创建动画
        const animation = particle.animate([
            { cx: sourceNode.x, cy: sourceNode.y, opacity: 0 },
            { cx: sourceNode.x + dx * 0.2, cy: sourceNode.y + dy * 0.2, opacity: 1 },
            { cx: targetNode.x - dx * 0.2, cy: targetNode.y - dy * 0.2, opacity: 1 },
            { cx: targetNode.x, cy: targetNode.y, opacity: 0 }
        ], {
            duration: duration,
            easing: 'ease-in-out'
        });

        animation.addEventListener('finish', () => {
            if (svg.contains(particle)) {
                svg.removeChild(particle);
            }
            // 循环创建粒子
            setTimeout(() => {
                this.createFlowParticle(svg, sourceNode, targetNode, color);
            }, Math.random() * 3000 + 2000);
        });
    }

    // 初始化热门数据资产
    initPopularAssets() {
        const container = document.getElementById('assets-grid');

        DATA_CONFIG.popularAssets.forEach((asset, index) => {
            const card = this.createAssetCard(asset);
            container.appendChild(card);

            // 延迟显示动画
            setTimeout(() => {
                card.style.opacity = '0';
                card.style.transform = 'translateX(-30px)';
                card.style.transition = 'all 0.5s ease';

                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateX(0)';
                }, 100);
            }, index * 150);
        });
    }

    createAssetCard(asset) {
        const card = document.createElement('div');
        card.className = 'asset-card';

        card.innerHTML = `
            <div class="asset-header">
                <i class="${asset.icon} asset-icon"></i>
                <span class="asset-type">${asset.type}</span>
            </div>
            <div class="asset-name">${asset.name}</div>
            <div class="asset-platform">
                <i class="fas fa-server"></i>
                <span>${asset.platform}</span>
            </div>
            <div class="asset-description">${asset.description}</div>
            <div class="asset-meta">
                <div>
                    <span class="asset-domain">${asset.domain}</span>
                    <span class="asset-usage ${asset.usage}">${this.getUsageText(asset.usage)}</span>
                </div>
                <div>
                    <small>记录: ${asset.recordCount} | 更新: ${asset.lastUpdate}</small>
                </div>
            </div>
        `;

        card.addEventListener('click', () => {
            this.showAssetDetail(asset);
        });

        return card;
    }

    getUsageText(usage) {
        const usageMap = {
            'high': '高频使用',
            'medium': '中频使用',
            'low': '低频使用'
        };
        return usageMap[usage] || usage;
    }

    // 初始化数据质量
    initDataQuality() {
        const progressBars = document.querySelectorAll('.progress-fill');
        
        progressBars.forEach((bar, index) => {
            const progress = bar.getAttribute('data-progress');
            setTimeout(() => {
                bar.style.setProperty('--progress', progress + '%');
            }, index * 200);
        });
    }

    // 初始化搜索功能
    initSearch() {
        const searchInput = document.getElementById('search-input');
        let searchTimeout;
        
        searchInput.addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                this.performSearch(e.target.value);
            }, 300);
        });
        
        searchInput.addEventListener('focus', () => {
            this.showSearchSuggestions();
        });
    }

    performSearch(query) {
        if (!query.trim()) {
            this.hideSearchResults();
            return;
        }

        // 模拟搜索结果
        const results = this.searchData(query);
        this.showSearchResults(results);
    }

    searchData(query) {
        const results = [];
        const lowerQuery = query.toLowerCase();

        // 搜索业务域
        DATA_CONFIG.domains.forEach(domain => {
            if (domain.name.toLowerCase().includes(lowerQuery) ||
                domain.description.toLowerCase().includes(lowerQuery)) {
                results.push({
                    type: 'domain',
                    name: domain.name,
                    description: domain.description,
                    data: domain
                });
            }
        });

        // 搜索数据资产
        DATA_CONFIG.popularAssets.forEach(asset => {
            if (asset.name.toLowerCase().includes(lowerQuery) ||
                asset.description.toLowerCase().includes(lowerQuery) ||
                asset.type.toLowerCase().includes(lowerQuery)) {
                results.push({
                    type: 'asset',
                    name: asset.name,
                    description: asset.description,
                    data: asset
                });
            }
        });

        return results;
    }

    showSearchResults(results) {
        let suggestionsContainer = document.querySelector('.search-suggestions');

        if (!suggestionsContainer) {
            suggestionsContainer = document.createElement('div');
            suggestionsContainer.className = 'search-suggestions';
            document.querySelector('.search-container').appendChild(suggestionsContainer);
        }

        if (results.length === 0) {
            suggestionsContainer.innerHTML = '<div class="suggestion-item">未找到相关结果</div>';
        } else {
            suggestionsContainer.innerHTML = results.map(result => `
                <div class="suggestion-item" data-type="${result.type}" data-name="${result.name}">
                    <strong>${result.name}</strong>
                    <br>
                    <small>${result.description}</small>
                </div>
            `).join('');

            // 添加点击事件
            suggestionsContainer.querySelectorAll('.suggestion-item').forEach(item => {
                item.addEventListener('click', () => {
                    const type = item.getAttribute('data-type');
                    const name = item.getAttribute('data-name');
                    const data = results.find(r => r.name === name).data;

                    if (type === 'domain') {
                        this.showDomainDetail(data);
                    } else if (type === 'asset') {
                        this.showAssetDetail(data);
                    }

                    this.hideSearchResults();
                });
            });
        }

        suggestionsContainer.style.display = 'block';
    }

    hideSearchResults() {
        const suggestionsContainer = document.querySelector('.search-suggestions');
        if (suggestionsContainer) {
            suggestionsContainer.style.display = 'none';
        }
    }

    showSearchSuggestions() {
        const suggestions = SEARCH_CONFIG.suggestions;
        let suggestionsContainer = document.querySelector('.search-suggestions');

        if (!suggestionsContainer) {
            suggestionsContainer = document.createElement('div');
            suggestionsContainer.className = 'search-suggestions';
            document.querySelector('.search-container').appendChild(suggestionsContainer);
        }

        suggestionsContainer.innerHTML = suggestions.map(suggestion => `
            <div class="suggestion-item">${suggestion}</div>
        `).join('');

        // 添加点击事件
        suggestionsContainer.querySelectorAll('.suggestion-item').forEach(item => {
            item.addEventListener('click', () => {
                document.getElementById('search-input').value = item.textContent;
                this.performSearch(item.textContent);
            });
        });

        suggestionsContainer.style.display = 'block';
    }

    // 初始化模态框
    initModal() {
        const modal = document.getElementById('detail-modal');
        const closeBtn = modal.querySelector('.close');
        
        closeBtn.addEventListener('click', () => {
            this.hideModal();
        });
        
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.hideModal();
            }
        });
    }

    // 显示域详情
    showDomainDetail(domain) {
        const modalBody = document.getElementById('modal-body');
        modalBody.innerHTML = `
            <h2><i class="${domain.icon}"></i> ${domain.name}</h2>
            <p>${domain.description}</p>
            <div class="domain-detail-stats">
                <div class="stat-item">
                    <span class="stat-number">${domain.assetCount}</span>
                    <span class="stat-label">数据资产</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">${domain.apiCount}</span>
                    <span class="stat-label">API接口</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">${domain.reportCount}</span>
                    <span class="stat-label">报表</span>
                </div>
            </div>
            <h3>子域包含:</h3>
            <ul>
                ${domain.subDomains.map(sub => `<li>${sub}</li>`).join('')}
            </ul>
            <h3>资产类型:</h3>
            <div class="asset-type-tags">
                ${domain.assetTypes.map(type => `<span class="asset-type-tag">${type}</span>`).join('')}
            </div>
        `;
        this.showModal();
    }

    // 显示资产详情
    showAssetDetail(asset) {
        const modalBody = document.getElementById('modal-body');
        modalBody.innerHTML = `
            <h2><i class="${asset.icon}"></i> ${asset.name}</h2>
            <div class="asset-detail-header">
                <span class="asset-type-badge">${asset.type}</span>
                <span class="asset-domain-badge">${asset.domain}</span>
                <span class="asset-platform-badge">${asset.platform}</span>
            </div>
            <p><strong>描述:</strong> ${asset.description}</p>
            <div class="asset-detail-stats">
                <div class="stat-item">
                    <span class="stat-label">数据平台</span>
                    <span class="stat-number">${asset.platform}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">记录数/状态</span>
                    <span class="stat-number">${asset.recordCount}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">最后更新</span>
                    <span class="stat-number">${asset.lastUpdate}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">使用频率</span>
                    <span class="stat-number usage-${asset.usage}">${this.getUsageText(asset.usage)}</span>
                </div>
            </div>
            ${this.getAssetTypeSpecificInfo(asset)}
        `;
        this.showModal();
    }

    getAssetTypeSpecificInfo(asset) {
        switch(asset.type) {
            case 'MySQL表':
                return `
                    <h3>MySQL表信息:</h3>
                    <p>• 存储引擎: InnoDB</p>
                    <p>• 字符集: UTF8MB4</p>
                    <p>• 索引策略: 主键+业务索引</p>
                    <p>• 备份策略: 每日全量备份</p>
                `;
            case 'PostgreSQL表':
                return `
                    <h3>PostgreSQL表信息:</h3>
                    <p>• 表空间: 默认表空间</p>
                    <p>• 分区策略: 按时间分区</p>
                    <p>• 索引类型: B-tree索引</p>
                    <p>• 统计信息: 自动更新</p>
                `;
            case 'FineReport报表':
                return `
                    <h3>FineReport报表信息:</h3>
                    <p>• 报表类型: 分页报表</p>
                    <p>• 数据源: 多数据源</p>
                    <p>• 导出格式: PDF, Excel, Word</p>
                    <p>• 刷新策略: 定时刷新</p>
                `;
            case 'FineBI仪表板':
            case 'FineBI分析':
                return `
                    <h3>FineBI分析信息:</h3>
                    <p>• 组件数量: 6-12个</p>
                    <p>• 数据更新: 实时/定时</p>
                    <p>• 交互功能: 钻取、联动、筛选</p>
                    <p>• 移动端: 支持响应式</p>
                `;
            case 'FineDataLink管道':
                return `
                    <h3>FineDataLink管道信息:</h3>
                    <p>• 任务类型: ETL数据处理</p>
                    <p>• 调度频率: 每小时执行</p>
                    <p>• 数据源: MySQL, PostgreSQL</p>
                    <p>• 目标: 数据仓库</p>
                `;
            case '简道云应用':
                return `
                    <h3>简道云应用信息:</h3>
                    <p>• 应用类型: 业务流程应用</p>
                    <p>• 表单数量: 15个</p>
                    <p>• 流程节点: 审批、通知</p>
                    <p>• 权限控制: 角色权限</p>
                `;
            default:
                return `
                    <h3>资产详细信息:</h3>
                    <p>• 平台: ${asset.platform}</p>
                    <p>• 类型: ${asset.type}</p>
                    <p>• 状态: 正常运行</p>
                `;
        }
    }

    showModal() {
        const modal = document.getElementById('detail-modal');
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden';
    }

    hideModal() {
        const modal = document.getElementById('detail-modal');
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }

    // 显示加载动画
    showLoading() {
        const loading = document.getElementById('loading-overlay');
        loading.style.display = 'flex';
    }

    // 隐藏加载动画
    hideLoading() {
        const loading = document.getElementById('loading-overlay');
        loading.style.opacity = '0';
        setTimeout(() => {
            loading.style.display = 'none';
        }, 500);
    }

    // 启动动画效果
    startAnimations() {
        // 启动各种动画效果
        this.startHeaderAnimation();
        this.startScrollAnimations();
    }

    startHeaderAnimation() {
        const logo = document.querySelector('.logo i');
        setInterval(() => {
            logo.style.transform = 'scale(1.2) rotate(360deg)';
            setTimeout(() => {
                logo.style.transform = 'scale(1) rotate(0deg)';
            }, 500);
        }, 5000);
    }

    startScrollAnimations() {
        // 滚动动画观察器
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        });

        // 观察所有需要动画的元素
        document.querySelectorAll('.domain-card, .table-card, .quality-card').forEach(el => {
            observer.observe(el);
        });
    }
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new DataMapApp();
});

// 添加一些全局样式类
const style = document.createElement('style');
style.textContent = `
    .animate-in {
        animation: slideInUp 0.6s ease-out;
    }
    
    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .high-usage { border-left: 3px solid #ff6b6b; }
    .medium-usage { border-left: 3px solid #f7b731; }
    .low-usage { border-left: 3px solid #4ecdc4; }
`;
document.head.appendChild(style);
